import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for Asana API settings"""
    
    ASANA_TOKEN = os.getenv('ASANA_TOKEN')
    ASANA_BASE_URL = 'https://app.asana.com/api/1.0'
    
    @classmethod
    def get_headers(cls):
        """Get headers for Asana API requests"""
        if not cls.ASANA_TOKEN or cls.ASANA_TOKEN == 'your_asana_token_here':
            raise ValueError("Please set your Asana token in the .env file")
        
        return {
            'Authorization': f'Bearer {cls.ASANA_TOKEN}',
            'Content-Type': 'application/json'
        }
