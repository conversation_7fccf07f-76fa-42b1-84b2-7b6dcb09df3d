import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
from asana_api import AsanaAPI

class AsanaDesktopApp:
    """Main desktop application for Asana API integration"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Asana API Desktop Application")
        self.root.geometry("1000x700")
        
        # Initialize API client
        try:
            self.api = AsanaAPI()
        except ValueError as e:
            messagebox.showerror("Configuration Error", str(e))
            self.root.destroy()
            return
        
        # Data storage
        self.workspaces = []
        self.projects = []
        self.tasks = []
        self.current_workspace = None
        self.current_project = None
        self.current_task = None
        
        self.setup_ui()
        self.load_workspaces()
    
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Workspace selection
        ttk.Label(main_frame, text="Workspace:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.workspace_var = tk.StringVar()
        self.workspace_combo = ttk.Combobox(main_frame, textvariable=self.workspace_var, 
                                          state="readonly", width=50)
        self.workspace_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        self.workspace_combo.bind('<<ComboboxSelected>>', self.on_workspace_selected)
        
        # Project selection
        ttk.Label(main_frame, text="Project:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.project_var = tk.StringVar()
        self.project_combo = ttk.Combobox(main_frame, textvariable=self.project_var, 
                                        state="readonly", width=50)
        self.project_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        self.project_combo.bind('<<ComboboxSelected>>', self.on_project_selected)
        
        # Tasks frame
        tasks_frame = ttk.LabelFrame(main_frame, text="Tasks", padding="5")
        tasks_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        tasks_frame.columnconfigure(0, weight=1)
        tasks_frame.rowconfigure(0, weight=1)
        
        # Tasks listbox with scrollbar
        self.tasks_listbox = tk.Listbox(tasks_frame, height=10)
        tasks_scrollbar = ttk.Scrollbar(tasks_frame, orient="vertical", command=self.tasks_listbox.yview)
        self.tasks_listbox.configure(yscrollcommand=tasks_scrollbar.set)
        self.tasks_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tasks_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tasks_listbox.bind('<<ListboxSelect>>', self.on_task_selected)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        ttk.Button(buttons_frame, text="View Subtasks", 
                  command=self.view_subtasks).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="View Comments", 
                  command=self.view_comments).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Refresh", 
                  command=self.refresh_data).pack(side=tk.LEFT, padx=5)
        
        # Details frame
        details_frame = ttk.LabelFrame(main_frame, text="Details", padding="5")
        details_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        details_frame.columnconfigure(0, weight=1)
        details_frame.rowconfigure(0, weight=1)
        
        # Details text area
        self.details_text = scrolledtext.ScrolledText(details_frame, height=15, wrap=tk.WORD)
        self.details_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights for resizing
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(4, weight=2)
    
    def load_workspaces(self):
        """Load workspaces from Asana API"""
        def fetch_workspaces():
            self.workspaces = self.api.get_workspaces()
            self.root.after(0, self.update_workspace_combo)
        
        # Run in separate thread to avoid blocking UI
        threading.Thread(target=fetch_workspaces, daemon=True).start()
    
    def update_workspace_combo(self):
        """Update workspace combobox with fetched data"""
        workspace_names = [ws['name'] for ws in self.workspaces]
        self.workspace_combo['values'] = workspace_names
        if workspace_names:
            self.workspace_combo.current(0)
            self.on_workspace_selected(None)
    
    def on_workspace_selected(self, event):
        """Handle workspace selection"""
        if not self.workspace_var.get():
            return
        
        # Find selected workspace
        workspace_name = self.workspace_var.get()
        self.current_workspace = next((ws for ws in self.workspaces if ws['name'] == workspace_name), None)
        
        if self.current_workspace:
            self.load_projects()
    
    def load_projects(self):
        """Load projects for selected workspace"""
        def fetch_projects():
            self.projects = self.api.get_projects(self.current_workspace['gid'])
            self.root.after(0, self.update_project_combo)
        
        threading.Thread(target=fetch_projects, daemon=True).start()
    
    def update_project_combo(self):
        """Update project combobox with fetched data"""
        project_names = [proj['name'] for proj in self.projects]
        self.project_combo['values'] = project_names
        if project_names:
            self.project_combo.current(0)
            self.on_project_selected(None)
    
    def on_project_selected(self, event):
        """Handle project selection"""
        if not self.project_var.get():
            return
        
        # Find selected project
        project_name = self.project_var.get()
        self.current_project = next((proj for proj in self.projects if proj['name'] == project_name), None)
        
        if self.current_project:
            self.load_tasks()
    
    def load_tasks(self):
        """Load tasks for selected project"""
        def fetch_tasks():
            self.tasks = self.api.get_project_tasks(self.current_project['gid'])
            self.root.after(0, self.update_tasks_listbox)
        
        threading.Thread(target=fetch_tasks, daemon=True).start()
    
    def update_tasks_listbox(self):
        """Update tasks listbox with fetched data"""
        self.tasks_listbox.delete(0, tk.END)
        for task in self.tasks:
            self.tasks_listbox.insert(tk.END, task['name'])
    
    def on_task_selected(self, event):
        """Handle task selection"""
        selection = self.tasks_listbox.curselection()
        if selection:
            index = selection[0]
            self.current_task = self.tasks[index]
            self.show_task_details()
    
    def show_task_details(self):
        """Show details of selected task"""
        if not self.current_task:
            return
        
        def fetch_details():
            task_details = self.api.get_task_details(self.current_task['gid'])
            if task_details:
                details_text = f"Task: {task_details['name']}\n"
                details_text += f"GID: {task_details['gid']}\n"
                details_text += f"Completed: {task_details.get('completed', 'N/A')}\n"
                details_text += f"Due Date: {task_details.get('due_on', 'N/A')}\n"
                details_text += f"Notes: {task_details.get('notes', 'No notes')}\n"
                
                self.root.after(0, lambda: self.update_details_text(details_text))
        
        threading.Thread(target=fetch_details, daemon=True).start()
    
    def update_details_text(self, text):
        """Update details text area"""
        self.details_text.delete(1.0, tk.END)
        self.details_text.insert(1.0, text)
    
    def view_subtasks(self):
        """View subtasks for selected task"""
        if not self.current_task:
            messagebox.showwarning("No Selection", "Please select a task first.")
            return
        
        def fetch_subtasks():
            subtasks = self.api.get_task_subtasks(self.current_task['gid'])
            subtasks_text = f"Subtasks for: {self.current_task['name']}\n"
            subtasks_text += "=" * 50 + "\n\n"
            
            if subtasks:
                for i, subtask in enumerate(subtasks, 1):
                    subtasks_text += f"{i}. {subtask['name']}\n"
                    subtasks_text += f"   GID: {subtask['gid']}\n"
                    subtasks_text += f"   Completed: {subtask.get('completed', 'N/A')}\n\n"
            else:
                subtasks_text += "No subtasks found.\n"
            
            self.root.after(0, lambda: self.update_details_text(subtasks_text))
        
        threading.Thread(target=fetch_subtasks, daemon=True).start()
    
    def view_comments(self):
        """View comments/stories for selected task"""
        if not self.current_task:
            messagebox.showwarning("No Selection", "Please select a task first.")
            return
        
        def fetch_comments():
            stories = self.api.get_task_stories(self.current_task['gid'])
            comments_text = f"Comments for: {self.current_task['name']}\n"
            comments_text += "=" * 50 + "\n\n"
            
            if stories:
                for i, story in enumerate(stories, 1):
                    if story.get('type') == 'comment':
                        comments_text += f"{i}. {story.get('text', 'No text')}\n"
                        comments_text += f"   Created: {story.get('created_at', 'N/A')}\n"
                        if 'created_by' in story:
                            comments_text += f"   By: {story['created_by'].get('name', 'Unknown')}\n"
                        comments_text += "\n"
            else:
                comments_text += "No comments found.\n"
            
            self.root.after(0, lambda: self.update_details_text(comments_text))
        
        threading.Thread(target=fetch_comments, daemon=True).start()
    
    def refresh_data(self):
        """Refresh all data"""
        self.load_workspaces()

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = AsanaDesktopApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
