# Asana API Desktop Application

A Python desktop application built with Tkinter that interfaces with the Asana API to manage workspaces, projects, tasks, subtasks, and comments.

## Features

- **Workspace Selection**: Browse and select from all available Asana workspaces
- **Project Management**: View projects within selected workspaces
- **Task Management**: Display all tasks within selected projects
- **Subtask Viewing**: View subtasks for any selected task
- **Comments/Stories**: Read comments and stories associated with tasks
- **Real-time Data**: All data is fetched in real-time from the Asana API
- **Threaded Operations**: Non-blocking UI with background API calls

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Asana API Token

1. Get your Asana Personal Access Token:
   - Go to [Asana Developer Console](https://app.asana.com/0/developer-console)
   - Create a new Personal Access Token
   - Copy the token

2. Update the `.env` file:
   ```
   ASANA_TOKEN=your_actual_asana_token_here
   ```

### 3. Run the Application

```bash
python main.py
```

## Usage

1. **Select Workspace**: Choose from the dropdown of available workspaces
2. **Select Project**: Choose a project from the selected workspace
3. **View Tasks**: Tasks will automatically load in the left panel
4. **Select Task**: Click on any task to view its details and automatically load subtasks
5. **View Subtasks**: Subtasks automatically appear in the right panel when a task is selected
6. **Select Subtask**: Click on any subtask to view its details
7. **View Comments**: Click "View Comments" to see all comments/stories for the selected task or subtask
8. **Refresh**: Click "Refresh" to reload all data from the API

## Interface Layout

- **Top Section**: Workspace and Project dropdowns
- **Middle Section**: Split view with Tasks (left) and Subtasks (right)
- **Bottom Section**: Details panel showing information about selected task/subtask

## API Endpoints Used

- `GET /workspaces` - Retrieve all workspaces
- `GET /projects?workspace={workspace_gid}` - Get projects in a workspace
- `GET /projects/{project_gid}/tasks` - Get tasks in a project
- `GET /tasks/{task_gid}/subtasks` - Get subtasks for a task
- `GET /tasks/{task_gid}/stories` - Get comments/stories for a task
- `GET /tasks/{task_gid}` - Get detailed task information

## File Structure

```
├── main.py           # Main GUI application
├── asana_api.py      # Asana API client
├── config.py         # Configuration management
├── requirements.txt  # Python dependencies
├── .env             # Environment variables (API token)
└── README.md        # This file
```

## Requirements

- Python 3.6+
- Asana Personal Access Token
- Internet connection for API calls

## Error Handling

The application includes error handling for:
- Missing or invalid API tokens
- Network connectivity issues
- API rate limiting
- Invalid API responses

## Security

- API tokens are stored in environment variables
- No sensitive data is logged or displayed in plain text
- Bearer token authentication is used for all API calls
