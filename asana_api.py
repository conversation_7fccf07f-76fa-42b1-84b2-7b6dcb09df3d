import requests
import json
from config import Config

class AsanaAPI:
    """Asana API client for desktop application"""
    
    def __init__(self):
        self.base_url = Config.ASANA_BASE_URL
        self.headers = Config.get_headers()
    
    def _make_request(self, endpoint):
        """Make a GET request to Asana API"""
        url = f"{self.base_url}{endpoint}"
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"API request failed: {e}")
            return None
    
    def get_workspaces(self):
        """Get all workspaces"""
        data = self._make_request('/workspaces')
        if data and 'data' in data:
            return data['data']
        return []
    
    def get_projects(self, workspace_gid):
        """Get all projects in a workspace"""
        data = self._make_request(f'/projects?workspace={workspace_gid}')
        if data and 'data' in data:
            return data['data']
        return []
    
    def get_project_tasks(self, project_gid):
        """Get all tasks in a project"""
        data = self._make_request(f'/projects/{project_gid}/tasks')
        if data and 'data' in data:
            return data['data']
        return []
    
    def get_task_subtasks(self, task_gid):
        """Get subtasks for a specific task"""
        data = self._make_request(f'/tasks/{task_gid}/subtasks')
        if data and 'data' in data:
            return data['data']
        return []
    
    def get_task_stories(self, task_gid):
        """Get stories (comments) for a specific task"""
        data = self._make_request(f'/tasks/{task_gid}/stories')
        if data and 'data' in data:
            return data['data']
        return []
    
    def get_task_details(self, task_gid):
        """Get detailed information about a task"""
        data = self._make_request(f'/tasks/{task_gid}')
        if data and 'data' in data:
            return data['data']
        return None
